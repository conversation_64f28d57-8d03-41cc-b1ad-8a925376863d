import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import {
  TextInput,
  Button,
  Title,
  Paragraph,
  Card,
  ActivityIndicator,
  Snackbar,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../context/AuthContext';
import { EnhancedCard } from '../components/EnhancedCard';
import { EnhancedButton } from '../components/EnhancedButton';
import { theme } from '../theme/theme';

export default function LoginScreen() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const { login } = useAuth();

  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      showSnackbar('Please enter both username and password');
      return;
    }

    setLoading(true);
    try {
      const result = await login(username.trim(), password);
      if (!result.success) {
        showSnackbar(result.message || 'Login failed');
      }
    } catch (error) {
      showSnackbar('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  return (
    <LinearGradient
      colors={theme.gradients.primary}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.logoContainer}>
            <View style={styles.iconContainer}>
              <Ionicons
                name="construct"
                size={80}
                color="white"
              />
            </View>
            <Title style={styles.title}>Flori Construction</Title>
            <Paragraph style={styles.subtitle}>Admin Panel</Paragraph>
          </View>

          <EnhancedCard
            style={styles.card}
            shadow="large"
            borderRadius={theme.borderRadius.xl}
            animated={true}
          >
            <Title style={styles.loginTitle}>Welcome Back</Title>
            <Paragraph style={styles.loginSubtitle}>
              Sign in to manage your construction projects
            </Paragraph>

            <TextInput
              label="Username or Email"
              value={username}
              onChangeText={setUsername}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="account" />}
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="email-address"
              returnKeyType="next"
              disabled={loading}
            />

            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              returnKeyType="done"
              onSubmitEditing={handleLogin}
              disabled={loading}
            />

            <EnhancedButton
              title={loading ? 'Signing In...' : 'Sign In'}
              variant="gradient"
              size="large"
              onPress={handleLogin}
              style={styles.loginButton}
              disabled={loading}
              loading={loading}
              icon="log-in-outline"
              fullWidth={true}
            />

            <View style={styles.helpContainer}>
              <Paragraph style={styles.helpText}>
                Default credentials: admin / admin123
              </Paragraph>
            </View>
          </EnhancedCard>
        </ScrollView>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={4000}
          style={styles.snackbar}
        >
          {snackbarMessage}
        </Snackbar>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: theme.spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xxxl,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  title: {
    color: 'white',
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 18,
    textAlign: 'center',
  },
  card: {
    marginHorizontal: theme.spacing.sm,
  },
  cardContent: {
    padding: theme.spacing.xl,
  },
  loginTitle: {
    textAlign: 'center',
    color: theme.colors.text,
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: theme.spacing.sm,
  },
  loginSubtitle: {
    textAlign: 'center',
    color: theme.colors.textSecondary,
    fontSize: 16,
    marginBottom: theme.spacing.xl,
    lineHeight: 24,
  },
  input: {
    marginBottom: theme.spacing.lg,
    backgroundColor: theme.colors.backgroundSecondary,
  },
  loginButton: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  helpContainer: {
    alignItems: 'center',
  },
  helpText: {
    color: theme.colors.placeholder,
    fontSize: 12,
    textAlign: 'center',
  },
  snackbar: {
    backgroundColor: theme.colors.error,
  },
});
