import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { config } from '../config/environment';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return false;
      }

      // Get push token
      this.expoPushToken = await this.getExpoPushToken();

      if (this.expoPushToken) {
        await SecureStore.setItemAsync('expoPushToken', this.expoPushToken);
        if (config.isDevelopment) {
          console.log('📱 Push token:', this.expoPushToken);
        }
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return false;
    }
  }

  async getExpoPushToken() {
    try {
      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        return null;
      }

      // For development, we'll skip push token generation if no projectId is available
      // In production, you should set up a proper EAS project with a valid projectId
      let projectId = Constants.expoConfig?.extra?.eas?.projectId ||
        Constants.expoConfig?.projectId ||
        Constants.easConfig?.projectId;

      if (!projectId) {
        console.warn('No EAS projectId found. Push notifications will not work in development mode.');
        console.warn('To enable push notifications, set up an EAS project and add the projectId to app.json');
        return null;
      }

      const token = (await Notifications.getExpoPushTokenAsync({
        projectId: projectId,
      })).data;

      return token;
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  setupNotificationListeners() {
    // Listener for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      if (config.isDevelopment) {
        console.log('📨 Notification received:', notification);
      }
      this.handleNotificationReceived(notification);
    });

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      if (config.isDevelopment) {
        console.log('👆 Notification tapped:', response);
      }
      this.handleNotificationResponse(response);
    });
  }

  handleNotificationReceived(notification) {
    const { title, body, data } = notification.request.content;

    // Handle different notification types
    switch (data?.type) {
      case 'new_message':
        this.handleNewMessageNotification(data);
        break;
      case 'project_update':
        this.handleProjectUpdateNotification(data);
        break;
      case 'service_update':
        this.handleServiceUpdateNotification(data);
        break;
      default:
        if (config.isDevelopment) {
          console.log('Unknown notification type:', data?.type);
        }
    }
  }

  handleNotificationResponse(response) {
    const { data } = response.notification.request.content;

    // Navigate to appropriate screen based on notification type
    switch (data?.type) {
      case 'new_message':
        // Navigate to messages screen
        this.navigateToScreen('Messages', { messageId: data.messageId });
        break;
      case 'project_update':
        // Navigate to project detail
        this.navigateToScreen('ProjectDetail', { project: { id: data.projectId } });
        break;
      case 'service_update':
        // Navigate to service detail
        this.navigateToScreen('ServiceDetail', { service: { id: data.serviceId } });
        break;
    }
  }

  handleNewMessageNotification(data) {
    // Update message badge count
    this.updateBadgeCount('messages');

    // Trigger any message-specific handlers
    if (this.onNewMessage) {
      this.onNewMessage(data);
    }
  }

  handleProjectUpdateNotification(data) {
    // Trigger project update handlers
    if (this.onProjectUpdate) {
      this.onProjectUpdate(data);
    }
  }

  handleServiceUpdateNotification(data) {
    // Trigger service update handlers
    if (this.onServiceUpdate) {
      this.onServiceUpdate(data);
    }
  }

  navigateToScreen(screenName, params = {}) {
    // This will be set by the navigation system
    if (this.navigationRef) {
      this.navigationRef.navigate(screenName, params);
    }
  }

  async updateBadgeCount(type) {
    try {
      const currentCount = await Notifications.getBadgeCountAsync();
      await Notifications.setBadgeCountAsync(currentCount + 1);
    } catch (error) {
      console.error('Error updating badge count:', error);
    }
  }

  async clearBadgeCount() {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('Error clearing badge count:', error);
    }
  }

  // Local notification methods
  async scheduleLocalNotification(title, body, data = {}, trigger = null) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: trigger || null, // null means immediate
      });

      if (config.isDevelopment) {
        console.log('📅 Local notification scheduled:', notificationId);
      }

      return notificationId;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return null;
    }
  }

  async cancelLocalNotification(notificationId) {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  async cancelAllLocalNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  // Push notification methods
  async sendPushNotification(expoPushToken, title, body, data = {}) {
    const message = {
      to: expoPushToken,
      sound: 'default',
      title,
      body,
      data,
    };

    try {
      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      const result = await response.json();

      if (config.isDevelopment) {
        console.log('📤 Push notification sent:', result);
      }

      return result;
    } catch (error) {
      console.error('Error sending push notification:', error);
      return null;
    }
  }

  // Notification preferences
  async getNotificationSettings() {
    try {
      const settings = await SecureStore.getItemAsync('notificationSettings');
      return settings ? JSON.parse(settings) : {
        enabled: true,
        sound: true,
        vibration: true,
        newMessages: true,
        projectUpdates: true,
        serviceUpdates: true,
      };
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return {};
    }
  }

  async updateNotificationSettings(settings) {
    try {
      await SecureStore.setItemAsync('notificationSettings', JSON.stringify(settings));

      // Update notification permissions based on settings
      if (!settings.enabled) {
        await this.cancelAllLocalNotifications();
      }

      return true;
    } catch (error) {
      console.error('Error updating notification settings:', error);
      return false;
    }
  }

  // Event handlers (to be set by the app)
  setNavigationRef(navigationRef) {
    this.navigationRef = navigationRef;
  }

  setEventHandlers({ onNewMessage, onProjectUpdate, onServiceUpdate }) {
    this.onNewMessage = onNewMessage;
    this.onProjectUpdate = onProjectUpdate;
    this.onServiceUpdate = onServiceUpdate;
  }

  // Cleanup
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }

  // Get current push token
  getPushToken() {
    return this.expoPushToken;
  }

  // Check if notifications are enabled
  async areNotificationsEnabled() {
    const { status } = await Notifications.getPermissionsAsync();
    return status === 'granted';
  }
}

export const notificationService = new NotificationService();
