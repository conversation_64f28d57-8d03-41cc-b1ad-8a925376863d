import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { apiService } from '../services/apiService';
import { AdvancedSearchBar } from '../components/SearchBar';
import { EnhancedCard } from '../components/EnhancedCard';
import { EnhancedButton } from '../components/EnhancedButton';
import { EnhancedLoader, ListSkeleton } from '../components/EnhancedLoading';
import { theme } from '../theme/theme';
import { useAuth } from '../context/AuthContext';

export default function ProjectsScreen({ navigation }) {
  const { loading: authLoading, isAuthenticated, token, user } = useAuth();
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);

  useEffect(() => {
    // Only load projects when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && token) {
      console.log('🔄 Auth state ready, loading projects...');
      // Add a small delay to ensure token is properly set
      const timer = setTimeout(() => {
        loadProjects();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [authLoading, isAuthenticated, token]);

  useEffect(() => {
    filterProjects();
  }, [projects, searchQuery, selectedFilters]);

  const filterProjects = () => {
    let filtered = [...projects];

    // Apply text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.location.toLowerCase().includes(query) ||
        (project.client && project.client.toLowerCase().includes(query))
      );
    }

    // Apply status filters
    if (selectedFilters.length > 0) {
      filtered = filtered.filter(project =>
        selectedFilters.includes(project.status)
      );
    }

    setFilteredProjects(filtered);
  };

  const handleSearch = (query, filters) => {
    setSearchQuery(query);
    setSelectedFilters(filters);
  };

  const loadProjects = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading projects...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken && token) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        console.log('🔑 Restoring token from AuthContext for projects');
        console.log('🔍 Token preview:', token.substring(0, 50) + '...');
        apiService.setAuthToken(token);
      }

      // Debug: Check current auth status before API call
      const authStatus = apiService.getAuthStatus();
      console.log('🔍 Auth status before projects API call:', authStatus);
      console.log('🔍 Full token being used:', authStatus.tokenPreview ? authStatus.tokenPreview.substring(0, 100) + '...' : 'null');

      const response = await apiService.getProjectsWithCache();
      if (response.success) {
        console.log('✅ Projects loaded successfully:', response.data.projects?.length || response.data?.length || 0, 'projects');
        setProjects(response.data.projects || response.data);
      } else {
        console.error('❌ Failed to load projects:', response.message);
        Alert.alert('Error', 'Failed to load projects');
      }
    } catch (error) {
      console.error('Error loading projects:', error);
      Alert.alert('Error', 'Failed to load projects');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadProjects();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.success;
      case 'ongoing':
        return theme.colors.warning;
      case 'planned':
        return theme.colors.info;
      default:
        return theme.colors.placeholder;
    }
  };

  const renderProject = ({ item }) => (
    <EnhancedCard
      style={styles.projectCard}
      onPress={() => navigation.navigate('ProjectDetail', { project: item })}
      shadow="medium"
      animated={true}
    >
      <View style={styles.projectHeader}>
        <Text style={styles.projectTitle}>{item.title}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      <View style={styles.projectInfo}>
        <View style={styles.projectInfoRow}>
          <Ionicons name="location-outline" size={16} color={theme.colors.primary} />
          <Text style={styles.projectLocation}>{item.location}</Text>
        </View>
        {item.client && (
          <View style={styles.projectInfoRow}>
            <Ionicons name="person-outline" size={16} color={theme.colors.primary} />
            <Text style={styles.projectClient}>{item.client}</Text>
          </View>
        )}
        {item.start_date && (
          <View style={styles.projectInfoRow}>
            <Ionicons name="calendar-outline" size={16} color={theme.colors.primary} />
            <Text style={styles.projectDate}>
              {new Date(item.start_date).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>
      <Text style={styles.projectDescription} numberOfLines={2}>
        {item.description}
      </Text>
    </EnhancedCard>
  );

  // Show loading while authentication is in progress
  if (authLoading) {
    return (
      <View style={styles.centerContainer}>
        <Text>Authenticating...</Text>
      </View>
    );
  }

  // Show loading while projects are being fetched
  if (loading && !refreshing) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Projects</Text>
          <View style={[styles.addButton, { backgroundColor: theme.colors.disabled }]}>
            <Ionicons name="add" size={24} color="white" />
          </View>
        </View>
        <View style={styles.searchContainer}>
          <View style={[styles.skeletonSearch, { height: 50 }]} />
        </View>
        <ListSkeleton count={6} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <Text style={styles.headerTitle}>Projects</Text>
        <EnhancedButton
          title=""
          variant="contained"
          size="small"
          onPress={() => navigation.navigate('AddProject')}
          icon="add"
          style={styles.addButton}
        />
      </LinearGradient>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <AdvancedSearchBar
          placeholder="Search projects..."
          onSearch={handleSearch}
          filters={[
            { label: 'Planned', value: 'planned', icon: 'time-outline' },
            { label: 'Ongoing', value: 'ongoing', icon: 'play-outline' },
            { label: 'Completed', value: 'completed', icon: 'checkmark-outline' },
          ]}
          selectedFilters={selectedFilters}
        />
      </View>

      <FlatList
        data={filteredProjects}
        renderItem={renderProject}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <Ionicons name="business-outline" size={80} color={theme.colors.primary} />
            </View>
            <Text style={styles.emptyText}>No projects found</Text>
            <Text style={styles.emptySubtext}>
              Start by creating your first construction project
            </Text>
            <EnhancedButton
              title="Add First Project"
              variant="gradient"
              size="large"
              onPress={() => navigation.navigate('AddProject')}
              icon="add-outline"
              style={styles.emptyButton}
            />
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    backgroundColor: 'white',
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  projectCard: {
    marginBottom: theme.spacing.md,
    marginHorizontal: theme.spacing.xs,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  projectTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  projectInfo: {
    marginBottom: theme.spacing.md,
  },
  projectInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  projectLocation: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  projectClient: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  projectDescription: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
  },
  projectDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xxxl,
    paddingHorizontal: theme.spacing.xl,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  emptyText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  emptySubtext: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
  },
  emptyButton: {
    marginTop: theme.spacing.md,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  skeletonSearch: {
    backgroundColor: theme.colors.borderLight,
    borderRadius: theme.borderRadius.lg,
    marginHorizontal: theme.spacing.md,
  },
});
