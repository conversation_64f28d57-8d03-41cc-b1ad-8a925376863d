import { DefaultTheme } from 'react-native-paper';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#ff6b35',
    primaryLight: '#ff8a5c',
    primaryDark: '#e55a2b',
    accent: '#f39c12',
    accentLight: '#f5b041',
    background: '#f8f9fa',
    backgroundSecondary: '#ffffff',
    surface: '#ffffff',
    surfaceVariant: '#f5f5f5',
    text: '#1a1a1a',
    textSecondary: '#666666',
    textLight: '#999999',
    disabled: '#cccccc',
    placeholder: '#999999',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#ff6b35',
    error: '#dc3545',
    errorLight: '#f8d7da',
    success: '#28a745',
    successLight: '#d4edda',
    warning: '#ffc107',
    warningLight: '#fff3cd',
    info: '#17a2b8',
    infoLight: '#d1ecf1',
    secondary: '#2c3e50',
    light: '#f8f9fa',
    dark: '#1a1a1a',
    border: '#e9ecef',
    borderLight: '#f1f3f4',
    overlay: 'rgba(0, 0, 0, 0.1)',
  },
  gradients: {
    primary: ['#ff6b35', '#ff8a5c'],
    primaryReverse: ['#ff8a5c', '#ff6b35'],
    secondary: ['#2c3e50', '#34495e'],
    success: ['#28a745', '#20c997'],
    warning: ['#ffc107', '#fd7e14'],
    error: ['#dc3545', '#e74c3c'],
    info: ['#17a2b8', '#20c997'],
    background: ['#f8f9fa', '#ffffff'],
    card: ['#ffffff', '#f8f9fa'],
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    semiBold: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    bold: {
      fontFamily: 'System',
      fontWeight: '700',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
  },
  typography: {
    h1: { fontSize: 32, fontWeight: '700', lineHeight: 40 },
    h2: { fontSize: 28, fontWeight: '600', lineHeight: 36 },
    h3: { fontSize: 24, fontWeight: '600', lineHeight: 32 },
    h4: { fontSize: 20, fontWeight: '600', lineHeight: 28 },
    h5: { fontSize: 18, fontWeight: '500', lineHeight: 24 },
    h6: { fontSize: 16, fontWeight: '500', lineHeight: 22 },
    body1: { fontSize: 16, fontWeight: '400', lineHeight: 24 },
    body2: { fontSize: 14, fontWeight: '400', lineHeight: 20 },
    caption: { fontSize: 12, fontWeight: '400', lineHeight: 16 },
    overline: { fontSize: 10, fontWeight: '500', lineHeight: 14, textTransform: 'uppercase' },
    button: { fontSize: 16, fontWeight: '600', lineHeight: 20 },
  },
  roundness: 12,
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
    xxxl: 64,
  },
  shadows: {
    none: {
      shadowColor: 'transparent',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
      elevation: 0,
    },
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 3,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 8,
      elevation: 6,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.16,
      shadowRadius: 16,
      elevation: 12,
    },
    floating: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.2,
      shadowRadius: 24,
      elevation: 20,
    },
  },
  animations: {
    timing: {
      fast: 150,
      normal: 250,
      slow: 350,
    },
    easing: {
      easeInOut: 'ease-in-out',
      easeOut: 'ease-out',
      easeIn: 'ease-in',
    },
  },
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    xxl: 24,
    full: 9999,
  },
};
