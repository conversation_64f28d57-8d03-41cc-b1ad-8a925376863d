import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  ActivityIndicator,
  Chip,
  Avatar,
  List,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';
import * as SecureStore from 'expo-secure-store';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { SimpleLoader } from '../components/LoadingScreen';
import { EnhancedCard } from '../components/EnhancedCard';
import { EnhancedLoader, ListSkeleton } from '../components/EnhancedLoading';
import { EnhancedButton } from '../components/EnhancedButton';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');

export default function DashboardScreen({ navigation }) {
  const [stats, setStats] = useState({});
  const [recentProjects, setRecentProjects] = useState([]);
  const [recentMessages, setRecentMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { user, logout } = useAuth();

  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      console.log('🔄 Loading dashboard data...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        // Try to get token from secure storage and set it
        try {
          const storedToken = await SecureStore.getItemAsync('authToken');
          if (storedToken) {
            console.log('🔑 Restoring token from secure storage');
            apiService.setAuthToken(storedToken);
          }
        } catch (error) {
          console.error('Failed to restore token:', error);
        }
      }

      // Load dashboard statistics with caching
      const dashboardResponse = await apiService.getDashboardStatsWithCache();
      console.log('📊 Dashboard response:', dashboardResponse);

      if (dashboardResponse.fromCache) {
        console.log('📦 Dashboard data loaded from cache');
      }

      if (dashboardResponse.success) {
        const data = dashboardResponse.data;

        // Set statistics
        setStats({
          totalProjects: data.projects.total,
          completedProjects: data.projects.completed,
          ongoingProjects: data.projects.ongoing,
          plannedProjects: data.projects.planned,
          newMessages: data.messages.new,
          totalMessages: data.messages.total,
          totalServices: data.services.total,
          totalMedia: data.media.total,
        });

        // Set recent data
        setRecentProjects(data.recent_projects || []);
        setRecentMessages(data.recent_messages || []);

        console.log('✅ Dashboard data loaded successfully');
      }
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });

      // Fallback to individual API calls with caching if dashboard endpoint fails
      try {
        console.log('🔄 Trying fallback API calls with cache...');
        const [projectsResponse, messagesResponse] = await Promise.all([
          apiService.getProjectsWithCache({ limit: 5 }),
          apiService.getMessagesWithCache({ status: 'new', limit: 5 }),
        ]);

        if (projectsResponse.success) {
          setRecentProjects(projectsResponse.data.projects);
          const projects = projectsResponse.data.projects;
          const completedCount = projects.filter(p => p.status === 'completed').length;
          const ongoingCount = projects.filter(p => p.status === 'ongoing').length;

          setStats({
            totalProjects: projectsResponse.data.pagination.total,
            completedProjects: completedCount,
            ongoingProjects: ongoingCount,
            newMessages: messagesResponse.success ? messagesResponse.data.pagination.total : 0,
          });

          console.log('✅ Fallback data loaded successfully');
        }

        if (messagesResponse.success) {
          setRecentMessages(messagesResponse.data.messages);
        }
      } catch (fallbackError) {
        console.error('❌ Fallback dashboard loading also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const testConnection = async () => {
    try {
      console.log('🧪 Testing API connection...');

      // Check auth status first
      const authStatus = apiService.getAuthStatus();
      console.log('🔑 Auth status:', authStatus);

      // Test basic connection first
      try {
        const testResponse = await apiService.testConnection();
        console.log('✅ Basic test response:', testResponse);
      } catch (testError) {
        console.log('❌ Basic test failed:', testError.message);
      }

      // Test debug endpoint
      try {
        const debugResponse = await apiService.debugConnection();
        console.log('🔍 Debug response:', debugResponse);

        if (debugResponse.success) {
          const data = debugResponse.data;
          alert(`Debug Info:
- Database: ${data.database_connection}
- Auth Token in Request: ${data.auth_token_found ? 'Found' : 'Missing'}
- Auth Token in App: ${authStatus.hasToken ? 'Found' : 'Missing'}
- Token Verification: ${data.token_verification || 'Not tested'}
- User Count: ${data.user_count || 'Unknown'}
- User Info: ${user ? user.username : 'Not logged in'}

Check console for full details.`);
        }
      } catch (debugError) {
        console.log('❌ Debug test failed:', debugError.message);
        alert('Debug test failed: ' + debugError.message);
      }

    } catch (error) {
      console.error('❌ Connection test failed:', error);
      alert('Connection test failed: ' + error.message);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleLogout = async () => {
    await logout();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.success;
      case 'ongoing':
        return theme.colors.warning;
      case 'planned':
        return theme.colors.info;
      default:
        return theme.colors.placeholder;
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        {/* Header Skeleton */}
        <LinearGradient
          colors={theme.gradients.primary}
          style={styles.headerSkeleton}
        >
          <View style={styles.headerContent}>
            <View>
              <View style={[styles.skeletonText, { width: 120, height: 18 }]} />
              <View style={[styles.skeletonText, { width: 80, height: 16, marginTop: 4 }]} />
            </View>
            <View style={[styles.skeletonButton, { width: 80, height: 36 }]} />
          </View>
        </LinearGradient>

        {/* Stats Skeleton */}
        <View style={styles.statsContainer}>
          <ListSkeleton count={2} />
        </View>

        <EnhancedLoader
          loading={true}
          text="Loading dashboard..."
          gradient={true}
          style={styles.centerLoader}
        />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View>
            <Title style={styles.welcomeTitle}>Welcome back,</Title>
            <Paragraph style={styles.userName}>{user?.username}</Paragraph>
          </View>
          <EnhancedButton
            title="Logout"
            variant="outlined"
            size="small"
            onPress={handleLogout}
            icon="log-out-outline"
            style={styles.logoutButton}
            textStyle={{ color: 'white' }}
          />
        </View>
      </LinearGradient>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statsRow}>
          <EnhancedCard
            gradient={true}
            gradientColors={theme.gradients.primary}
            style={styles.statCard}
            shadow="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <Ionicons name="business-outline" size={28} color="white" />
              <Title style={styles.statNumber}>{stats.totalProjects || 0}</Title>
            </View>
            <Paragraph style={styles.statLabel}>Total Projects</Paragraph>
          </EnhancedCard>

          <EnhancedCard
            gradient={true}
            gradientColors={theme.gradients.success}
            style={styles.statCard}
            shadow="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <Ionicons name="checkmark-circle-outline" size={28} color="white" />
              <Title style={styles.statNumber}>{stats.completedProjects || 0}</Title>
            </View>
            <Paragraph style={styles.statLabel}>Completed</Paragraph>
          </EnhancedCard>
        </View>

        <View style={styles.statsRow}>
          <EnhancedCard
            gradient={true}
            gradientColors={theme.gradients.warning}
            style={styles.statCard}
            shadow="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <Ionicons name="construct-outline" size={28} color="white" />
              <Title style={styles.statNumber}>{stats.ongoingProjects || 0}</Title>
            </View>
            <Paragraph style={styles.statLabel}>Ongoing</Paragraph>
          </EnhancedCard>

          <EnhancedCard
            gradient={true}
            gradientColors={theme.gradients.info}
            style={styles.statCard}
            shadow="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <Ionicons name="mail-outline" size={28} color="white" />
              <Title style={styles.statNumber}>{stats.newMessages || 0}</Title>
            </View>
            <Paragraph style={styles.statLabel}>New Messages</Paragraph>
          </EnhancedCard>
        </View>
      </View>

      {/* Recent Projects */}
      <EnhancedCard style={styles.sectionCard} shadow="medium" animated={true}>
        <View style={styles.sectionHeader}>
          <Title style={styles.sectionTitle}>Recent Projects</Title>
          <EnhancedButton
            title="View All"
            variant="text"
            size="small"
            onPress={() => navigation.navigate('Projects')}
            icon="arrow-forward-outline"
            iconPosition="right"
          />
        </View>

        {recentProjects.length > 0 ? (
          recentProjects.map((project) => (
            <List.Item
              key={project.id}
              title={project.title}
              description={project.location}
              left={(props) => (
                <Avatar.Icon
                  {...props}
                  icon="office-building"
                  style={{ backgroundColor: theme.colors.primary }}
                />
              )}
              right={() => (
                <Chip
                  style={{ backgroundColor: getStatusColor(project.status) }}
                  textStyle={{ color: 'white' }}
                >
                  {project.status}
                </Chip>
              )}
              onPress={() => navigation.navigate('ProjectDetail', { project })}
            />
          ))
        ) : (
          <Paragraph style={styles.emptyText}>No recent projects</Paragraph>
        )}
      </EnhancedCard>

      {/* Recent Messages */}
      <EnhancedCard style={styles.sectionCard} shadow="medium" animated={true}>
        <View style={styles.sectionHeader}>
          <Title style={styles.sectionTitle}>New Messages</Title>
          <EnhancedButton
            title="View All"
            variant="text"
            size="small"
            onPress={() => navigation.navigate('Messages')}
            icon="arrow-forward-outline"
            iconPosition="right"
          />
        </View>

        {recentMessages.length > 0 ? (
          recentMessages.map((message) => (
            <List.Item
              key={message.id}
              title={message.name}
              description={message.subject || 'No subject'}
              left={(props) => (
                <Avatar.Icon
                  {...props}
                  icon="account"
                  style={{ backgroundColor: theme.colors.accent }}
                />
              )}
              right={() => (
                <Paragraph style={styles.messageTime}>
                  {new Date(message.created_at).toLocaleDateString()}
                </Paragraph>
              )}
              onPress={() => navigation.navigate('MessageDetail', { message })}
            />
          ))
        ) : (
          <Paragraph style={styles.emptyText}>No new messages</Paragraph>
        )}
      </EnhancedCard>

      {/* Quick Actions */}
      <EnhancedCard style={styles.sectionCard} shadow="medium" animated={true}>
        <Title style={styles.sectionTitle}>Quick Actions</Title>
        <View style={styles.quickActions}>
          <EnhancedButton
            title="Add Project"
            variant="gradient"
            size="medium"
            onPress={() => navigation.navigate('AddProject')}
            icon="add-outline"
            style={styles.quickActionButton}
            fullWidth={false}
          />
          <EnhancedButton
            title="Add Service"
            variant="gradient"
            color="secondary"
            size="medium"
            onPress={() => navigation.navigate('AddService')}
            icon="add-outline"
            style={styles.quickActionButton}
            fullWidth={false}
          />
        </View>

        {/* Debug button - remove in production */}
        <View style={styles.debugActions}>
          <EnhancedButton
            title="Test API Connection"
            variant="outlined"
            size="small"
            onPress={testConnection}
            icon="bug-outline"
            style={styles.debugButton}
            fullWidth={true}
          />
        </View>
      </EnhancedCard>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
  },
  header: {
    backgroundColor: theme.colors.primary,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeTitle: {
    color: 'white',
    fontSize: 18,
  },
  userName: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontWeight: 'bold',
  },
  logoutButton: {
    borderColor: 'white',
  },
  statsContainer: {
    padding: theme.spacing.md,
    marginTop: -theme.spacing.lg,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    ...theme.shadows.medium,
  },
  statCardContent: {
    padding: theme.spacing.md,
  },
  statCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  statNumber: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  sectionCard: {
    margin: theme.spacing.md,
    marginTop: 0,
    ...theme.shadows.small,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  emptyText: {
    textAlign: 'center',
    color: theme.colors.placeholder,
    fontStyle: 'italic',
  },
  messageTime: {
    fontSize: 12,
    color: theme.colors.placeholder,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: theme.spacing.md,
  },
  quickActionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  debugActions: {
    marginTop: theme.spacing.lg,
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.disabled,
  },
  debugButton: {
    borderColor: theme.colors.warning,
  },
  headerSkeleton: {
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  skeletonText: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: theme.borderRadius.sm,
  },
  skeletonButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: theme.borderRadius.md,
  },
  centerLoader: {
    marginTop: theme.spacing.xxxl,
  },
});
