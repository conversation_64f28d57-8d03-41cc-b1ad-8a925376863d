{"version": 3, "file": "NativeLinearGradient.types.js", "sourceRoot": "", "sources": ["../src/NativeLinearGradient.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PropsWithChildren } from 'react';\nimport { ColorValue, ViewProps } from 'react-native';\n\nexport type NativeLinearGradientProps = ViewProps &\n  PropsWithChildren<{\n    colors: readonly ColorValue[];\n    locations?: readonly number[] | null;\n    startPoint?: NativeLinearGradientPoint | null;\n    endPoint?: NativeLinearGradientPoint | null;\n    dither?: boolean;\n  }>;\n\nexport type getLinearGradientBackgroundImage = (\n  colors: readonly ColorValue[],\n  width?: number,\n  height?: number,\n  locations?: readonly number[] | null,\n  startPoint?: NativeLinearGradientPoint | null,\n  endPoint?: NativeLinearGradientPoint | null\n) => string;\n\nexport type NativeLinearGradientPoint = [x: number, y: number];\n"]}